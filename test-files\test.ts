// Test TypeScript file
interface TestInterface {
    name: string;
    value: number;
}

export class TestClass implements TestInterface {
    constructor(public name: string, public value: number) {}
    
    testMethod(): void {
        console.log(`${this.name}: ${this.value}`);
    }
}

export const testFunction = (data: TestInterface): void => {
    console.log('This is a test TypeScript file');
};
